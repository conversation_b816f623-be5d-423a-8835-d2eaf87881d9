
# Overlay Backtester (Starter)

This is a ready-to-run project to generate **pure/naïve options overlay** tear sheets and a deck for **SPX / QQQ / IWM**.

## Quick Start (CLI)

```bash
cd overlay_backtester
python3 -m venv .venv
source .venv/bin/activate   # Windows: .\.venv\Scripts\Activate.ps1
pip install -r requirements.txt
python run.py --etfs spx,qqq,iwm --strategies all --mode pure
```

Outputs go to `outputs/<timestamp>/`.

Generated: 2025-08-23 17:49
