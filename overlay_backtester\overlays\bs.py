
import math
from math import log, sqrt, exp
from scipy.stats import norm

def bs_call_price(S, K, r, sigma, T):
    if T<=0 or sigma<=0: return max(S-K,0.0)
    d1=(log(S/K)+(r+0.5*sigma*sigma)*T)/(sigma*sqrt(T)); d2=d1-sigma*sqrt(T)
    return S*norm.cdf(d1) - K*math.exp(-r*T)*norm.cdf(d2)

def bs_put_price(S, K, r, sigma, T):
    if T<=0 or sigma<=0: return max(K-S,0.0)
    d1=(log(S/K)+(r+0.5*sigma*sigma)*T)/(sigma*sqrt(T)); d2=d1-sigma*sqrt(T)
    return K*math.exp(-r*T)*norm.cdf(-d2) - S*norm.cdf(-d1)

def strike_from_call_delta(S, r, sigma, T, delta):
    if T<=0 or sigma<=0: return S
    from scipy.stats import norm
    d1 = norm.ppf(delta)
    return S*math.exp((r+0.5*sigma*sigma)*T - sigma*math.sqrt(T)*d1)

def strike_from_put_absdelta(S, r, sigma, T, abs_delta):
    if T<=0 or sigma<=0: return S
    from scipy.stats import norm
    d1 = norm.ppf(1-abs_delta)
    return S*math.exp((r+0.5*sigma*sigma)*T - sigma*math.sqrt(T)*d1)
