
import numpy as np
import pandas as pd

def generate_market(start="2017-01-01", end="2025-08-01", seed=11, drift_ann=0.09, vol_ann_low=0.16, vol_ann_high=0.32, base=2000.0):
    import math
    rng = np.random.default_rng(seed)
    dates = pd.bdate_range(start=start, end=end)
    n = len(dates)
    regime = np.zeros(n, dtype=int)
    for i in range(1, n):
        regime[i] = 1 - regime[i-1] if rng.random() < 0.004 else regime[i-1]
    mu = np.where(regime==0, drift_ann/252.0, -0.03/252.0)
    sigma_d = np.where(regime==0, vol_ann_low/np.sqrt(252), vol_ann_high/np.sqrt(252))
    eps = rng.normal(0,1,n)
    ar = 0.12
    z = np.zeros(n)
    for i in range(1, n):
        z[i] = ar*z[i-1] + (1-ar**2)**0.5*eps[i]
    rets = mu + sigma_d*z
    S = base*np.exp(np.cumsum(rets))
    idx = pd.Series(S, index=dates, name="PX")
    rvol = pd.Series(rets, index=dates).rolling(21).std()*np.sqrt(252)
    iv = (0.65*100*rvol.fillna(rvol.mean()) + np.where(regime==1, 7, 0) + 11).clip(10,60)
    rf = pd.Series(0.012+0.012*np.sin(np.linspace(0,3.14*1.2,n)), index=dates, name="RF")
    return pd.concat([idx, iv.rename("IV"), rf], axis=1)

def load_markets():
    return {
        "SPX": generate_market(seed=11, drift_ann=0.09, vol_ann_low=0.16, vol_ann_high=0.32, base=2200.0),
        "QQQ": generate_market(seed=13, drift_ann=0.11, vol_ann_low=0.20, vol_ann_high=0.40, base=130.0),
        "IWM": generate_market(seed=17, drift_ann=0.08, vol_ann_low=0.18, vol_ann_high=0.36, base=120.0),
    }

def third_fridays(idx: pd.DatetimeIndex):
    months = pd.period_range(idx.min(), idx.max(), freq="M")
    fridays = []
    for m in months:
        ds = pd.date_range(m.start_time, m.end_time, freq="W-FRI")
        d = ds[2] if len(ds)>=3 else ds[-1]
        while d not in idx and d>idx.min():
            d = d - pd.Timedelta(days=1)
        fridays.append(d)
    return fridays
