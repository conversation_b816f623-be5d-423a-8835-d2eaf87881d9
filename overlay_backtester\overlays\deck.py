
import matplotlib.pyplot as plt
import pandas as pd
from pptx import Presentation
from pptx.util import Inches

def _pct(x, digits=1):
    try:
        return f"{x*100:.{digits}f}%"
    except Exception:
        return str(x)

def _fmt_table(df):
    df = df.copy()
    if "CAGR" in df: df["CAGR"] = df["CAGR"].map(lambda x: _pct(x,1))
    if "AnnVol" in df: df["AnnVol"] = df["AnnVol"].map(lambda x: _pct(x,1))
    if "MaxDD" in df: df["MaxDD"] = df["MaxDD"].map(lambda x: _pct(x,1))
    if "UpsideCapture" in df: df["UpsideCapture"] = df["UpsideCapture"].map(lambda x: f"{x:.2f}")
    if "DownsideCapture" in df: df["DownsideCapture"] = df["DownsideCapture"].map(lambda x: f"{x:.2f}")
    if "Beta" in df: df["Beta"] = df["Beta"].map(lambda x: f"{x:.2f}")
    if "Corr" in df: df["Corr"] = df["Corr"].map(lambda x: f"{x:.2f}")
    if "VaR95" in df: df["VaR95"] = df["VaR95"].map(lambda x: _pct(x,1))
    if "CVaR95" in df: df["CVaR95"] = df["CVaR95"].map(lambda x: _pct(x,1))
    if "Sharpe" in df: df["Sharpe"] = df["Sharpe"].map(lambda x: f"{x:.2f}")
    if "Sortino" in df: df["Sortino"] = df["Sortino"].map(lambda x: f"{x:.2f}")
    if "Calmar" in df: df["Calmar"] = df["Calmar"].map(lambda x: f"{x:.2f}")
    if "SharpeLift" in df: df["SharpeLift"] = df["SharpeLift"].map(lambda x: f"{x:+.2f}")
    if "MaxDD_Improvement" in df: df["MaxDD_Improvement"] = df["MaxDD_Improvement"].map(lambda x: _pct(x,1))
    return df

def _add_definitions_slide(prs):
    s = prs.slides.add_slide(prs.slide_layouts[5])
    s.shapes.title.text = "Metrics Explained"
    txt = (
        "• CAGR: ((∏(1+r_t))^(1/years) − 1).\n"
        "• AnnVol: √252 · StdDev(daily returns) — realized vol of the overlay (not market IV).\n"
        "• Sharpe: E[r−r_f]/σ; Sortino: E[r−r_f]/σ_down.\n"
        "• MaxDD: min_t(Equity_t / max_{s≤t} Equity_s − 1).\n"
        "• Calmar: CAGR / |MaxDD|.\n"
        "• Upside/Downside Capture: overlay vs ETF during up/down months.\n"
        "• VaR95/CVaR95: 5th percentile monthly return and tail average (monthly, %)."
    )
    tf = s.shapes.add_textbox(Inches(0.4), Inches(1.2), Inches(9), Inches(5)).text_frame
    tf.word_wrap = True; tf.text = txt

def build_deck(overall_df, outdir):
    deck_path = f"{outdir}/Overlay_Pure_Deck.pptx"
    prs = Presentation()

    slide = prs.slides.add_slide(prs.slide_layouts[0])
    slide.shapes.title.text = "Systematic ETF Overlays — Pure Strategies (SPX / QQQ / IWM)"
    slide.placeholders[1].text = (
        "Equity vs ETF, Excess, Rolling Sharpe, Drawdowns, Capture, Tail Risk, Stress Months.\n"
        "AnnVol is realized volatility of the overlay. Protective overlays should show lower AnnVol than ETF."
    )

    # Per-ETF tables (formatted)
    for etf in sorted(overall_df['ETF'].unique()):
        cols = ["ETF","Strategy","CAGR","AnnVol","Sharpe","Sortino","MaxDD","Calmar","UpsideCapture","DownsideCapture","Beta","Corr","VaR95","CVaR95","SharpeLift","MaxDD_Improvement"]
        df = overall_df[overall_df['ETF']==etf][cols]
        df_fmt = _fmt_table(df)

        s = prs.slides.add_slide(prs.slide_layouts[5])
        s.shapes.title.text = f"{etf} — Comparison (Pure, formatted)"
        tf = s.shapes.add_textbox(Inches(0.4), Inches(1.2), Inches(9.3), Inches(5)).text_frame
        tf.word_wrap = True; tf.text = df_fmt.to_string(index=False)

    # Top value-add slide (Sharpe lift and DD improvement)
    s = prs.slides.add_slide(prs.slide_layouts[5])
    s.shapes.title.text = "Which strategies add the most value?"
    # rank across all ETFs (excluding baseline)
    df_all = overall_df[overall_df["Strategy"]!="Long-Only Baseline"].copy()
    top_sharpe = df_all.sort_values("SharpeLift", ascending=False).head(5)[["ETF","Strategy","SharpeLift","CAGR","AnnVol"]]
    top_dd = df_all.sort_values("MaxDD_Improvement", ascending=False).head(5)[["ETF","Strategy","MaxDD_Improvement","CAGR","AnnVol"]]
    top_sharpe_fmt = top_sharpe.copy(); top_sharpe_fmt["SharpeLift"] = top_sharpe_fmt["SharpeLift"].map(lambda x: f"{x:+.2f}")
    top_sharpe_fmt["CAGR"] = top_sharpe_fmt["CAGR"].map(lambda x: _pct(x,1)); top_sharpe_fmt["AnnVol"] = top_sharpe_fmt["AnnVol"].map(lambda x: _pct(x,1))
    top_dd_fmt = top_dd.copy(); top_dd_fmt["MaxDD_Improvement"] = top_dd_fmt["MaxDD_Improvement"].map(lambda x: _pct(x,1))
    top_dd_fmt["CAGR"] = top_dd_fmt["CAGR"].map(lambda x: _pct(x,1)); top_dd_fmt["AnnVol"] = top_dd_fmt["AnnVol"].map(lambda x: _pct(x,1))

    tf = s.shapes.add_textbox(Inches(0.4), Inches(1.2), Inches(4.5), Inches(4.8)).text_frame
    tf.word_wrap = True; tf.text = "Top Sharpe Lift:\n" + top_sharpe_fmt.to_string(index=False)
    tf2 = s.shapes.add_textbox(Inches(5.1), Inches(1.2), Inches(4.5), Inches(4.8)).text_frame
    tf2.word_wrap = True; tf2.text = "Top MaxDD Improvement:\n" + top_dd_fmt.to_string(index=False)

    # Frontier
    plt.figure()
    for etf in sorted(overall_df['ETF'].unique()):
        pts = overall_df[overall_df['ETF']==etf]
        plt.scatter(pts["AnnVol"], pts["CAGR"], label=etf)
    plt.title("Return vs Volatility — Efficient Frontier (Pure)")
    frontier = f"{outdir}/frontier.png"
    plt.savefig(frontier, bbox_inches="tight"); plt.close()

    s=prs.slides.add_slide(prs.slide_layouts[5])
    s.shapes.title.text="Return vs Volatility — Efficient Frontier (Pure)"
    s.shapes.add_picture(frontier, Inches(0.5), Inches(1.2), width=Inches(9))

    # Definitions slide at end
    _add_definitions_slide(prs)

    prs.save(deck_path)
    return deck_path
