
import numpy as np
import pandas as pd
from .strategies import open_legs
from .metrics import ann_ret, ann_vol, sharpe, sortino, max_dd, calmar, monthly_stats, stress_table

def run_pure(df, rolls, strategy, params=None):
    params = params or {}
    df = df.copy()
    df["Ret"] = df["PX"].pct_change().fillna(0.0)
    capital0=1_000_000.0; equity=[]; realized=pd.Series(0.0, index=df.index)
    T = params.get("tenor_days",30)/365.0
    prev_legs=None

    for i, (d,row) in enumerate(df.iterrows()):
        S=row["PX"]; r=row["RF"]; sig=row["IV"]/100.0
        if d in rolls:
            if prev_legs is not None:
                pnl=0.0
                for l in prev_legs:
                    payoff = max(S-l.K,0.0) if l.typ=="call" else max(l.K-S,0.0)
                    prem_out = -payoff if l.side=="long" else payoff
                    pnl += (l.prem_in + prem_out)*l.qty
                realized.loc[d]+=pnl

            # open
            if strategy=="Dynamic Vol Harvest":
                rv = df["Ret"].rolling(21).std().shift(1).loc[d]*np.sqrt(252)
                if pd.notna(rv) and (sig - rv) > params.get("iv_minus_rv_thresh",0.05):
                    legs = open_legs(strategy, S, r, sig, T, params=params)
                else:
                    legs = []
            else:
                legs = open_legs(strategy, S, r, sig, T, params=params)

            gross=sum(abs(l.prem_in) for l in legs)
            fee = gross*(params.get("fee_bps",1.0)+params.get("slippage_bps",2.0))/10000.0 if gross>0 else 0.0
            if gross>0:
                for l in legs:
                    l.prem_in -= fee*(abs(l.prem_in)/gross)
            prev_legs = legs

        eq = capital0 if i==0 else equity[-1]*(1+df["Ret"].iloc[i])
        if d in rolls and realized.loc[d]!=0.0:
            eq += realized.loc[d]
        equity.append(eq)

    eq=pd.Series(equity,index=df.index,name="Equity")
    dly=eq.pct_change().fillna(0.0)

    base = (1_000_000.0*(1+df["Ret"]).cumprod())
    base_d = base.pct_change().fillna(0.0)
    cum_overlay=(1+dly).cumprod(); cum_base=(1+base_d).cumprod(); excess=(cum_overlay/cum_base)-1.0

    m_eq = eq.resample("M").last()
    overlay_m = m_eq.pct_change().fillna(0.0)
    base_m = base.resample("M").last().pct_change().fillna(0.0)
    mstats = monthly_stats(overlay_m, base_m)
    stress = stress_table(overlay_m, base_m, n=4)

    summary = {
        "CAGR": float(ann_ret(dly)), "AnnVol": float(ann_vol(dly)),
        "Sharpe": float(sharpe(dly)), "Sortino": float(sortino(dly)),
        "MaxDD": float(max_dd(eq)), "Calmar": float(calmar(eq,dly)),
        "FinalEquity": float(eq.iloc[-1]), "BaseCAGR": float(ann_ret(base_d)),
        "SharpeLift": float(sharpe(dly)-sharpe(base_d)),
        "MaxDD_Improvement": float(max_dd(eq)-max_dd(base)),
        **{k: float(v) if v==v else float('nan') for k,v in mstats.items()}
    }

    monthly = pd.DataFrame({"Equity":eq}).resample("M").last()
    monthly["UnderlyingRet"] = df["Ret"].resample("M").sum()
    monthly["RealizedOptionPnL"] = 0.0
    monthly["OverlayRet"] = monthly["Equity"].pct_change().fillna(0.0)
    monthly["LongOnlyRet"] = base.resample("M").last().pct_change().fillna(0.0)
    monthly["Excess_vs_LongOnly"] = monthly["OverlayRet"] - monthly["LongOnlyRet"]

    return {"equity":eq,"daily":dly,"base_eq":base,"base_daily":base_d,"excess":excess,"monthly":monthly,"summary":summary,"stress":stress}

def run_switched(df, rolls, policy_name, params=None):
    """
    policy_name:
      - "Gate-VolSell": overlay ON only when RiskOn_Trend==1 AND IV_minus_RV > 3
                         strategy: Covered Call
      - "Gate-Defensive": overlay ON only when RiskOff_Bear==1 OR HighVol==1
                          strategy: Zero-Cost Collar
      - "Rotate-CC-vs-ZCC": if RiskOn_Trend==1 AND IV_minus_RV > 3 -> Covered Call
                            elif RiskOff_Bear==1 OR HighVol==1 -> Zero-Cost Collar
                            else -> OFF
    """
    params = params or {}
    from .signals import compute_signals
    from .strategies import open_legs

    df = df.copy()
    sig = compute_signals(df)
    df["Ret"] = df["PX"].pct_change().fillna(0.0)

    capital0=1_000_000.0; equity=[]; realized=pd.Series(0.0, index=df.index)
    prev_legs=None
    T = params.get("tenor_days",30)/365.0
    regime_at_roll = []  # for attribution

    for i, (d,row) in enumerate(df.iterrows()):
        S=row["PX"]; r=row["RF"]; iv=row["IV"]/100.0

        if d in rolls:
            # Close previous
            if prev_legs is not None:
                pnl=0.0
                for l in prev_legs:
                    payoff = max(S-l.K,0.0) if l.typ=="call" else max(l.K-S,0.0)
                    prem_out = -payoff if l.side=="long" else payoff
                    pnl += (l.prem_in + prem_out)*l.qty
                realized.loc[d]+=pnl
            # Decide ON/OFF and which package
            cc_on = (sig.loc[d, "RiskOn_Trend"]==1) and (sig.loc[d, "IV_minus_RV"]>3.0)
            def_on = (sig.loc[d, "RiskOff_Bear"]==1) or (sig.loc[d, "HighVol"]==1)
            legs = []
            chosen = "OFF"

            if policy_name=="Gate-VolSell":
                if cc_on:
                    legs = open_legs("Covered Call", S, r, iv, T, params=params.get("Covered Call", {}))
                    chosen="Covered Call"
            elif policy_name=="Gate-Defensive":
                if def_on:
                    legs = open_legs("Zero-Cost Collar", S, r, iv, T, params=params.get("Zero-Cost Collar", {}))
                    chosen="Zero-Cost Collar"
            elif policy_name=="Rotate-CC-vs-ZCC":
                if cc_on:
                    legs = open_legs("Covered Call", S, r, iv, T, params=params.get("Covered Call", {}))
                    chosen="Covered Call"
                elif def_on:
                    legs = open_legs("Zero-Cost Collar", S, r, iv, T, params=params.get("Zero-Cost Collar", {}))
                    chosen="Zero-Cost Collar"
                else:
                    chosen="OFF"
            else:
                raise ValueError(f"Unknown policy: {policy_name}")

            regime_at_roll.append((d, sig.loc[d,"RegimeLabel"], chosen))

            # Fees/slippage
            gross=sum(abs(l.prem_in) for l in legs)
            fee = gross*(params.get("fee_bps",1.0)+params.get("slippage_bps",2.0))/10000.0 if gross>0 else 0.0
            if gross>0:
                for l in legs:
                    l.prem_in -= fee*(abs(l.prem_in)/gross)
            prev_legs = legs

        # Daily equity
        eq = capital0 if i==0 else equity[-1]*(1+df["Ret"].iloc[i])
        if d in rolls and realized.loc[d]!=0.0:
            eq += realized.loc[d]
        equity.append(eq)

    eq=pd.Series(equity,index=df.index,name="Equity")
    dly=eq.pct_change().fillna(0.0)

    base = (1_000_000.0*(1+df["Ret"]).cumprod())
    base_d = base.pct_change().fillna(0.0)
    cum_overlay=(1+dly).cumprod(); cum_base=(1+base_d).cumprod(); excess=(cum_overlay/cum_base)-1.0

    m_eq = eq.resample("M").last()
    overlay_m = m_eq.pct_change().fillna(0.0)
    base_m = base.resample("M").last().pct_change().fillna(0.0)
    mstats = monthly_stats(overlay_m, base_m)
    stress = stress_table(overlay_m, base_m, n=4)

    summary = {
        "CAGR": float(ann_ret(dly)), "AnnVol": float(ann_vol(dly)),
        "Sharpe": float(sharpe(dly)), "Sortino": float(sortino(dly)),
        "MaxDD": float(max_dd(eq)), "Calmar": float(calmar(eq,dly)),
        "FinalEquity": float(eq.iloc[-1]), "BaseCAGR": float(ann_ret(base_d)),
        "SharpeLift": float(sharpe(dly)-sharpe(base_d)),
        "MaxDD_Improvement": float(max_dd(eq)-max_dd(base)),
        **{k: float(v) if v==v else float('nan') for k,v in mstats.items()}
    }

    monthly = pd.DataFrame({"Equity":eq}).resample("M").last()
    monthly["UnderlyingRet"] = df["Ret"].resample("M").sum()
    monthly["RealizedOptionPnL"] = 0.0
    monthly["OverlayRet"] = monthly["Equity"].pct_change().fillna(0.0)
    monthly["LongOnlyRet"] = base.resample("M").last().pct_change().fillna(0.0)
    monthly["Excess_vs_LongOnly"] = monthly["OverlayRet"] - monthly["LongOnlyRet"]

    # Regime attribution table on roll dates
    reg_df = pd.DataFrame(regime_at_roll, columns=["RollDate","Regime","Chosen"])
    if not reg_df.empty:
        counts = reg_df.groupby(["Regime","Chosen"]).size().reset_index(name="Count")
    else:
        counts = pd.DataFrame(columns=["Regime","Chosen","Count"])

    return {"equity":eq,"daily":dly,"base_eq":base,"base_daily":base_d,"excess":excess,"monthly":monthly,"summary":summary,"stress":stress,"regime_counts":counts}
