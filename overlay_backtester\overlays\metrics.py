
import numpy as np
import pandas as pd
import math

def ann_ret(d): comp=(1+d).prod(); yrs=len(d)/252.0; return comp**(1/yrs)-1
def ann_vol(d): return d.std()*math.sqrt(252)
def sharpe(d): mu=d.mean()*252; sd=d.std()*math.sqrt(252); return mu/sd if sd>0 else float('nan')
def sortino(d): dn=d[d<0].std()*math.sqrt(252); mu=d.mean()*252; return mu/dn if dn>0 else float('nan')
def max_dd(eq): return (eq/eq.cummax()-1).min()
def calmar(eq, d): dd=abs(max_dd(eq)); return (ann_ret(d)/dd) if dd>0 else float('nan')
def dd_curve(eq): return eq/eq.cummax()-1.0

def monthly_stats(overlay_m, base_m):
    pos = base_m > 0; neg = base_m < 0
    up_cap = (overlay_m[pos].mean()/base_m[pos].mean()) if pos.any() and base_m[pos].mean()!=0 else float('nan')
    down_cap = (overlay_m[neg].mean()/base_m[neg].mean()) if neg.any() and base_m[neg].mean()!=0 else float('nan')
    cov = np.cov(overlay_m.dropna(), base_m.dropna())[0,1]
    var = np.var(base_m.dropna())
    beta = cov/var if var>0 else float('nan')
    if len(overlay_m.dropna())==len(base_m.dropna()) and len(base_m.dropna())>2:
        corr = np.corrcoef(overlay_m.dropna(), base_m.dropna())[0,1]
    else:
        corr = float('nan')
    q = np.quantile(overlay_m.dropna(), 0.05) if len(overlay_m.dropna())>10 else float('nan')
    cvar = overlay_m[overlay_m<=q].mean() if not np.isnan(q) else float('nan')
    skew = ((overlay_m - overlay_m.mean())**3).mean() / (overlay_m.std()**3) if overlay_m.std()>0 else float('nan')
    kurt = ((overlay_m - overlay_m.mean())**4).mean() / (overlay_m.std()**4) if overlay_m.std()>0 else float('nan')
    return dict(UpsideCapture=up_cap, DownsideCapture=down_cap, Beta=beta, Corr=corr, VaR95=q, CVaR95=cvar, Skew=skew, Kurtosis=kurt)

def stress_table(overlay_m, base_m, n=4):
    tb = pd.DataFrame({"ETF": base_m, "Overlay": overlay_m}).dropna()
    return tb.nsmallest(n, "ETF")
