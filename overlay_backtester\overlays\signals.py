
import numpy as np
import pandas as pd

def ema(series, span):
    return series.ewm(span=span, adjust=False).mean()

def rsi(close, period=14):
    # Standard RSI on closes
    delta = close.diff()
    up = (delta.where(delta>0, 0.0)).rolling(period).mean()
    down = (-delta.where(delta<0, 0.0)).rolling(period).mean()
    rs = up / down
    return 100 - (100 / (1 + rs))

def realized_vol(returns, window=21):
    return returns.rolling(window).std()*np.sqrt(252)

def iv_percentile(iv, lookback=252):
    # Rolling percentile of the last value within the past window
    def pct(a):
        a = np.array(a)
        if len(a)<5: return np.nan
        rank = (a<=a[-1]).sum()-1
        return 100.0*rank/len(a)
    return iv.rolling(lookback).apply(pct, raw=False)

def compute_signals(df):
    # df: columns PX (close), IV (%), RF
    out = pd.DataFrame(index=df.index)
    out["EMA50"] = ema(df["PX"], 50)
    out["EMA200"] = ema(df["PX"], 200)
    out["Trend"] = (out["EMA50"] > out["EMA200"]).astype(int)

    out["RSI14"] = rsi(df["PX"], 14)
    rets = df["PX"].pct_change().fillna(0.0)
    out["RV21"] = realized_vol(rets, 21) * 100.0  # convert to vol points like IV
    out["IV"] = df["IV"]
    out["IV_minus_RV"] = out["IV"] - out["RV21"]
    out["IV_pctile"] = iv_percentile(out["IV"], 252)

    # Regime flags
    out["RiskOn_Trend"] = ((out["Trend"]==1) & (out["RSI14"]>55)).astype(int)
    out["RiskOff_Bear"] = ((out["Trend"]==0) | (out["RSI14"]<45)).astype(int)
    out["HighVol"] = (out["IV_pctile"]>80).astype(int)
    out["LowVol"] = (out["IV_pctile"]<30).astype(int)

    # Compact label for diagnostics
    def label_row(r):
        if r["RiskOff_Bear"]==1 or r["HighVol"]==1:
            return "Risk-Off/HighVol"
        if r["RiskOn_Trend"]==1:
            return "Risk-On Trend"
        return "Neutral"
    out["RegimeLabel"] = out.apply(label_row, axis=1)

    return out
