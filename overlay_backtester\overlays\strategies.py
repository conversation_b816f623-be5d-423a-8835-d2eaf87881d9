
from dataclasses import dataclass
from .bs import bs_call_price, bs_put_price, strike_from_call_delta, strike_from_put_absdelta

@dataclass
class Leg:
    typ:str
    side:str
    K:float
    T0:float
    qty:float
    prem_in:float
    prem_out:float=0.0

def open_legs(strategy, S, r, sig, T, params=None):
    params = params or {}
    legs = []
    def add_leg(typ, side, K, Tenor, qty=1.0):
        price = bs_call_price(S,K,r,sig,Tenor) if typ=="call" else bs_put_price(S,K,r,sig,Tenor)
        prem_in = -price if side=="long" else price
        legs.append(Leg(typ, side, float(K), float(Tenor), float(qty), float(prem_in)))

    if strategy=="Covered Call":
        Kc = strike_from_call_delta(S,r,sig,T, params.get("call_delta",0.25)); add_leg("call","short",Kc,T)
    elif strategy=="Zero-Cost Collar":
        Kc = strike_from_call_delta(S,r,sig,T,0.20); Kp = strike_from_put_absdelta(S,r,sig,T,0.25)
        add_leg("call","short",Kc,T); add_leg("put","long",Kp,T)
    elif strategy=="Cash-Secured Put":
        Kp = strike_from_put_absdelta(S,r,sig,T,0.25); add_leg("put","short",Kp,T)
    elif strategy=="Put Spread Collar":
        add_leg("put","long",0.95*S,T); add_leg("put","short",0.90*S,T); add_leg("call","short",1.05*S,T)
    elif strategy=="Protective Put":
        add_leg("put","long",0.95*S,T)
    elif strategy=="Put Spread":
        add_leg("put","long",S,T); add_leg("put","short",0.90*S,T)
    elif strategy=="Iron Condor":
        Kc_s = strike_from_call_delta(S,r,sig,T,0.20); Kp_s = strike_from_put_absdelta(S,r,sig,T,0.20)
        Kc_l = strike_from_call_delta(S,r,sig,T,0.10); Kp_l = strike_from_put_absdelta(S,r,sig,T,0.10)
        Kc_l = max(Kc_l, Kc_s*1.02); Kp_l = min(Kp_l, Kp_s*0.98)
        add_leg("call","short",Kc_s,T); add_leg("call","long",Kc_l,T)
        add_leg("put","short",Kp_s,T);  add_leg("put","long",Kp_l,T)
    elif strategy=="Diagonal Call":
        add_leg("call","long",S,3*30/365.0); Kc = strike_from_call_delta(S,r,sig,T,0.25); add_leg("call","short",Kc,T)
    elif strategy=="Calendar Call":
        K_long = strike_from_call_delta(S,r,sig,3*30/365.0,0.25); add_leg("call","long",K_long,3*30/365.0); Kc = strike_from_call_delta(S,r,sig,T,0.25); add_leg("call","short",Kc,T)
    elif strategy=="Dynamic Vol Harvest":
        Kc = strike_from_call_delta(S,r,sig,T,0.25); add_leg("call","short",Kc,T)
    elif strategy=="Tail Hedge Tranche":
        Kp = strike_from_put_absdelta(S,r,sig,T,0.10); add_leg("put","long",Kp,T,qty=0.01)
    else:
        raise ValueError(f"Unknown strategy: {strategy}")
    return legs
