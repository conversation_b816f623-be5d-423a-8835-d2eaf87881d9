
import math
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
from .metrics import dd_curve

def _pct(x, digits=1):
    try:
        return f"{x*100:.{digits}f}%"
    except Exception:
        return str(x)

def _fmt_tail(x, digits=1):
    # Tail risk (VaR/CVaR) are monthly returns -> %
    try:
        return f"{x*100:.{digits}f}%"
    except Exception:
        return str(x)

def make_tearsheet(etf, strat, res, outdir):
    eq=res["equity"]; base=res["base_eq"]; excess=res["excess"]; dly=res["daily"]; base_d=res["base_daily"]
    monthly=res["monthly"]; s=res["summary"]; stress=res["stress"]
    path=f"{outdir}/{etf}_{strat.replace(' ','_')}_TearSheet.pdf"

    with PdfPages(path) as pdf:
        # Page 1: Method & overview
        plt.figure(); plt.axis("off")
        text=(f"{etf} – {strat} (Pure Overlay, Always On)\n\n"
              "Objective: Systematic bolt-on overlay to enhance Sharpe / reduce drawdowns while keeping ~1× ETF beta.\n"
              "Method: Monthly European cash settlement (3rd Friday). Black–Scholes pricing with IV proxy; fees/slippage applied.\n"
              "Attribution: Overlay realized P&L booked at expiry; cumulative excess vs ETF tracks out/under-performance.")
        plt.text(0.02,0.98,text,va="top"); pdf.savefig(); plt.close()

        # Equity vs ETF
        plt.figure(); plt.plot(base.index, base.values, label=f"{etf} Long-Only"); plt.plot(eq.index, eq.values, label=strat)
        plt.title("Equity Curve"); plt.legend(); pdf.savefig(); plt.close()

        # Cumulative Excess vs ETF
        plt.figure(); plt.plot(excess.index, excess.values); plt.title(f"Cumulative Excess vs {etf}"); pdf.savefig(); plt.close()

        # Drawdowns
        plt.figure(); plt.plot(dd_curve(base).index, dd_curve(base).values, label=f"{etf}"); plt.plot(dd_curve(eq).index, dd_curve(eq).values, label=strat)
        plt.title("Drawdowns"); plt.legend(); pdf.savefig(); plt.close()

        # Rolling Sharpe (12m)
        plt.figure()
        rs=(dly.rolling(252).mean()*252)/(dly.rolling(252).std()*math.sqrt(252))
        brs=(base_d.rolling(252).mean()*252)/(base_d.rolling(252).std()*math.sqrt(252))
        plt.plot(brs.index, brs.values, label=f"{etf}"); plt.plot(rs.index, rs.values, label=strat); plt.title("Rolling Sharpe (12m)"); plt.legend(); pdf.savefig(); plt.close()

        # Monthly return distribution
        mret = eq.resample("M").last().pct_change().dropna()
        plt.figure(); plt.hist(mret.values, bins=30); plt.title("Monthly Return Distribution"); pdf.savefig(); plt.close()

        # Option income bars
        plt.figure(); ser = monthly["RealizedOptionPnL"]
        if len(ser.dropna())>0: plt.bar(ser.index, ser.values)
        plt.title("Realized Option P&L at Rolls (Monthly)"); pdf.savefig(); plt.close()

        # Capture ratios & tail risk (formatted)
        def get(name): 
            return s.get(name, float('nan'))
        rows = pd.DataFrame({
            "UpsideCapture":[get("UpsideCapture")],
            "DownsideCapture":[get("DownsideCapture")],
            "Beta":[get("Beta")],
            "Corr":[get("Corr")],
            "VaR95 (monthly)":[get("VaR95")],
            "CVaR95 (monthly)":[get("CVaR95")]
        })
        fmt_rows = rows.copy()
        for col in ["UpsideCapture","DownsideCapture","Beta","Corr"]:
            fmt_rows[col] = fmt_rows[col].map(lambda x: f"{x:.2f}" if pd.notna(x) else "NA")
        for col in ["VaR95 (monthly)","CVaR95 (monthly)"]:
            fmt_rows[col] = fmt_rows[col].map(lambda x: _fmt_tail(x,1) if pd.notna(x) else "NA")

        plt.figure(); plt.axis("off"); plt.title("Capture, Beta/Corr, Tail Risk (formatted)")
        plt.text(0.01,0.90,fmt_rows.to_string(index=False),va="top",family="monospace"); pdf.savefig(); plt.close()

        # Stress months
        plt.figure(); plt.axis("off"); plt.title("Stress Test – Worst ETF Months")
        try:
            pdf_tbl = res["stress"].round(4).to_string()
        except Exception as e:
            pdf_tbl = f"(insufficient samples) {e}"
        plt.text(0.01,0.90, pdf_tbl, va="top", family="monospace"); pdf.savefig(); plt.close()

        # Summary metrics (formatted as percentages)
        plt.figure(); plt.axis("off")
        lines=[
            f"CAGR {_pct(s['CAGR'],1)} vs ETF {_pct(s['BaseCAGR'],1)} | Sharpe {s['Sharpe']:.2f} (lift {s['SharpeLift']:+.2f})",
            f"AnnVol {_pct(s['AnnVol'],1)} | Sortino {s['Sortino']:.2f} | MaxDD {_pct(s['MaxDD'],1)} (Δ {_pct(s['MaxDD_Improvement'],1)}) | Calmar {s['Calmar']:.2f}",
            f"Final Equity ${s['FinalEquity']:,.0f}"
        ]
        note = ("\nNote: AnnVol is the realized annualized volatility of the OVERLAY (strategy equity). "
                "Downside-protection overlays (e.g., collars/puts) generally reduce AnnVol versus the ETF baseline.")
        plt.text(0.02,0.95,"\n".join(lines)+ "\n\n" + note,va="top"); pdf.savefig(); plt.close()

        # Metrics Explained (footnotes)
        plt.figure(); plt.axis("off")
        foot = (
        "Metrics Explained:\n"
        "• CAGR: ((∏(1+r_t))^(1/years) − 1). Annualized % return of the overlay.\n"
        "• AnnVol: √252 · StdDev(daily returns) — realized vol of the overlay (not the market IV).\n"
        "• Sharpe: E[r−r_f]/σ; Sortino: E[r−r_f]/σ_down.\n"
        "• MaxDD: min_t(Equity_t / max_{s≤t} Equity_s − 1).\n"
        "• Calmar: CAGR / |MaxDD|.\n"
        "• Upside Capture: mean(overlay up-months)/mean(ETF up-months). <1 ⇒ upside give-up.\n"
        "• Downside Capture: mean(overlay down-months)/mean(ETF down-months). <1 ⇒ better downside protection.\n"
        "• VaR95/CVaR95: 5th percentile monthly return and tail average (monthly, %).\n"
        )
        plt.text(0.02,0.98,foot,va="top"); pdf.savefig(); plt.close()

    return path


def make_tearsheet_switched(etf, policy, res, outdir):
    eq=res["equity"]; base=res["base_eq"]; excess=res["excess"]; dly=res["daily"]; base_d=res["base_daily"]
    monthly=res["monthly"]; s=res["summary"]; stress=res["stress"]
    path=f"{outdir}/{etf}_{policy.replace(' ','_')}_Switched_TearSheet.pdf"

    with PdfPages(path) as pdf:
        # Overview
        plt.figure(); plt.axis("off")
        text=(f"{etf} – {policy} (Regime-Switched)\n\n"
              "Overlay is turned ON/OFF by daily signals (RSI, EMA cross, IV−RV, IV percentile). "
              "Risk-On Trend → prefer vol-harvest (e.g., CC); Risk-Off/HighVol → prefer defensive (e.g., ZCC). "
              "Excess vs ETF isolates value-add from the switching policy.")
        plt.text(0.02,0.98,text,va="top"); pdf.savefig(); plt.close()

        # Equity
        plt.figure(); plt.plot(base.index, base.values, label=f"{etf} Long-Only"); plt.plot(eq.index, eq.values, label=policy)
        plt.title("Equity Curve"); plt.legend(); pdf.savefig(); plt.close()

        # Excess
        plt.figure(); plt.plot(excess.index, excess.values); plt.title(f"Cumulative Excess vs {etf}"); pdf.savefig(); plt.close()

        # Drawdowns
        plt.figure(); plt.plot(dd_curve(base).index, dd_curve(base).values, label=f"{etf}"); plt.plot(dd_curve(eq).index, dd_curve(eq).values, label=policy)
        plt.title("Drawdowns"); plt.legend(); pdf.savefig(); plt.close()

        # Rolling Sharpe
        plt.figure()
        rs=(dly.rolling(252).mean()*252)/(dly.rolling(252).std()*math.sqrt(252))
        brs=(base_d.rolling(252).mean()*252)/(base_d.rolling(252).std()*math.sqrt(252))
        plt.plot(brs.index, brs.values, label=f"{etf}"); plt.plot(rs.index, rs.values, label=policy); plt.title("Rolling Sharpe (12m)"); plt.legend(); pdf.savefig(); plt.close()

        # Regime counts table
        plt.figure(); plt.axis("off"); plt.title("Regime Attribution (roll-date counts)")
        reg = res.get("regime_counts")
        if reg is not None and not reg.empty:
            plt.text(0.01,0.90, reg.to_string(index=False), va="top", family="monospace")
        else:
            plt.text(0.01,0.90, "(No regime data recorded)")
        pdf.savefig(); plt.close()

        # Summary metrics (percent formatting)
        def _pct(x, d=1): 
            try: return f"{x*100:.{d}f}%"
            except: return str(x)
        plt.figure(); plt.axis("off")
        lines=[
            f"CAGR {_pct(s['CAGR'],1)} vs ETF {_pct(s['BaseCAGR'],1)} | Sharpe {s['Sharpe']:.2f} (lift {s['SharpeLift']:+.2f})",
            f"AnnVol {_pct(s['AnnVol'],1)} | Sortino {s['Sortino']:.2f} | MaxDD {_pct(s['MaxDD'],1)} (Δ {_pct(s['MaxDD_Improvement'],1)}) | Calmar {s['Calmar']:.2f}",
            f"Final Equity ${s['FinalEquity']:,.0f}"
        ]
        note = ("AnnVol is the realized annualized volatility of the OVERLAY product. "
                "Switching should further reduce drawdown/vol relative to always-on overlays by avoiding bad regimes.")
        plt.text(0.02,0.95,"\n".join(lines)+ "\n\n" + note,va="top"); pdf.savefig(); plt.close()

        # Metrics Explained
        plt.figure(); plt.axis("off")
        foot = (
        "Metrics Explained:\n"
        "• CAGR: ((∏(1+r_t))^(1/years) − 1). Annualized % return.\n"
        "• AnnVol: √252 · StdDev(daily returns) — realized vol of overlay.\n"
        "• Sharpe/Sortino: risk-adjusted returns.\n"
        "• MaxDD & Calmar: drawdown and return per unit drawdown.\n"
        "• Excess vs ETF: overlay − ETF; positive means value-add."
        )
        plt.text(0.02,0.98,foot,va="top"); pdf.savefig(); plt.close()

    return path
