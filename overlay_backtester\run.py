
import argparse, os, datetime as dt
import numpy as np
import pandas as pd

from overlays.data import load_markets, third_fridays
from overlays.engine import run_pure
from overlays.tearsheets import make_tearsheet
from overlays.deck import build_deck
from overlays.metrics import ann_ret, ann_vol, sharpe, sortino, max_dd, calmar

STRATS = [
    "Long-Only Baseline",
    "Covered Call","Zero-Cost Collar","Cash-Secured Put","Put Spread Collar",
    "Protective Put","Put Spread","Iron Condor","Diagonal Call","Calendar Call",
    "Dynamic Vol Harvest","Tail Hedge Tranche"
]
PARAMS = {
    "Covered Call": {"call_delta":0.25,"tenor_days":30},
    "Dynamic Vol Harvest":{"iv_minus_rv_thresh":0.05,"tenor_days":30},
}

def main():
    p = argparse.ArgumentParser(description="Generate pure/naïve overlay tear sheets and deck.")
    p.add_argument("--etfs", default="spx,qqq,iwm", help="Comma-separated: spx,qqq,iwm")
    p.add_argument("--strategies", default="all", help='"all" or comma-separated list of strategy names')
    p.add_argument("--mode", default="pure", choices=["pure"], help="Currently only 'pure' supported")
    p.add_argument("--outdir", default=None, help="Optional output directory (default: outputs/<timestamp>)")
    args = p.parse_args()

    etfs = [e.strip().upper() for e in args.etfs.split(",") if e.strip()]
    strategies = STRATS if args.strategies.lower()=="all" else [s.strip() for s in args.strategies.split(",")]

    outdir = args.outdir or os.path.join("outputs", dt.datetime.now().strftime("%Y-%m-%d_%H%M%S"))
    os.makedirs(outdir, exist_ok=True)

    markets = load_markets()
    rows=[]
    for etf in etfs:
        df = markets[etf]
        rolls = third_fridays(df.index)

        # Baseline (no overlay)
        base_df = df.copy()
        base_df["Ret"] = base_df["PX"].pct_change().fillna(0.0)
        base_eq = (1_000_000.0*(1+base_df["Ret"]).cumprod())
        base_daily = base_eq.pct_change().fillna(0.0)
        base_monthly = pd.DataFrame({"Equity":base_eq}).resample("M").last()
        base_monthly["UnderlyingRet"]=base_df["Ret"].resample("M").sum()
        base_monthly["RealizedOptionPnL"]=0.0

        for strat in strategies:
            if strat=="Long-Only Baseline":
                res = {
                    "equity": base_eq, "daily": base_daily, "base_eq": base_eq, "base_daily": base_daily,
                    "excess": pd.Series(0.0, index=base_eq.index),
                    "monthly": base_monthly,
                    "summary":{
                        "CAGR": float(ann_ret(base_daily)), "AnnVol": float(ann_vol(base_daily)),
                        "Sharpe": float(sharpe(base_daily)), "Sortino": float(sortino(base_daily)),
                        "MaxDD": float(max_dd(base_eq)), "Calmar": float(calmar(base_eq, base_daily)),
                        "FinalEquity": float(base_eq.iloc[-1]),
                        "BaseCAGR": float(ann_ret(base_daily)), "SharpeLift": 0.0, "MaxDD_Improvement": 0.0,
                        "UpsideCapture": 1.0, "DownsideCapture": 1.0, "Beta": 1.0, "Corr": 1.0, "VaR95": float("nan"), "CVaR95": float("nan"), "Skew": float("nan"), "Kurtosis": float("nan")
                    },
                    "stress": pd.DataFrame({"ETF": base_monthly["Equity"].pct_change().fillna(0.0)}).nsmallest(4,"ETF")
                }
            else:
                res = run_pure(df, rolls, strat, params=PARAMS.get(strat, {}))

            res["monthly"].to_csv(os.path.join(outdir, f"{etf}_{strat.replace(' ','_')}_monthly.csv"))
            make_tearsheet(etf, strat, res, outdir)

            rows.append({"ETF": etf, "Strategy": strat, **res["summary"]})

    
    overall = pd.DataFrame(rows)
    # Save raw
    overall.to_csv(os.path.join(outdir, "overall_summary_raw.csv"), index=False)

    # Save formatted version (percentages)
    def _pct(x, digits=1):
        try:
            return f"{x*100:.{digits}f}%"
        except Exception:
            return x
    fmt = overall.copy()
    for col in ["CAGR","AnnVol","MaxDD","MaxDD_Improvement","VaR95","CVaR95"]:
        if col in fmt.columns:
            fmt[col] = fmt[col].map(lambda x: _pct(x,1) if pd.notna(x) else x)
    for col in ["Sharpe","Sortino","Calmar","UpsideCapture","DownsideCapture","Beta","Corr","SharpeLift"]:
        if col in fmt.columns:
            fmt[col] = fmt[col].map(lambda x: f"{x:.2f}" if pd.notna(x) else x)

    fmt.to_csv(os.path.join(outdir, "overall_summary_formatted.csv"), index=False)

    deck_path = build_deck(overall, outdir)
    print("Outputs in:", outdir)
    print("Deck:", deck_path)
    
if __name__ == "__main__":
    main()
