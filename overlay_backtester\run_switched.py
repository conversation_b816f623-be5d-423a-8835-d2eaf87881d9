
import os, datetime as dt, pandas as pd
from overlays.data import load_markets, third_fridays
from overlays.engine import run_switched
from overlays.tearsheets import make_tearsheet_switched

POLICIES = ["Gate-VolSell","Gate-Defensive","Rotate-CC-vs-ZCC"]

def main():
    outdir = os.path.join("outputs", dt.datetime.now().strftime("%Y-%m-%d_%H%M%S") + "_switched")
    os.makedirs(outdir, exist_ok=True)

    markets = load_markets()
    df = markets["SPX"]
    rolls = third_fridays(df.index)

    rows = []
    for pol in POLICIES:
        res = run_switched(df, rolls, pol, params={
            "Covered Call": {"tenor_days":30},
            "Zero-Cost Collar": {"tenor_days":30}
        })
        path = make_tearsheet_switched("SPX", pol, res, outdir)
        rows.append({"ETF":"SPX","Policy":pol, **res["summary"]})

    pd.DataFrame(rows).to_csv(os.path.join(outdir,"switched_summary_raw.csv"), index=False)
    print("Outputs:", outdir)

if __name__=="__main__":
    main()
